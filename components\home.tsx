// app/Home.tsx
import { useState } from "react";
import { RobotStatusCard } from "@/components/ui/RobotStatusCard";
import { ParametersCard } from "@/components/ui/ParametersCard";
import { ActionButtons } from "@/components/ui/ActionButtons";
import { MapView } from "@/components/ui/MapView";
import { DirectionControls } from "@/components/ui/DirectionControls";
import { CameraView } from "@/components/ui/CameraView";
import { SpaceConfigModal, SpaceConfigData } from "@/components/ui/SpaceConfigModal";
import { SterilizationConfigModal, SterilizationConfigData } from "@/components/ui/SterilizationConfigModal";
import { StartSterilizationButton } from "@/components/ui/StartSterilizationButton";

export function Home() {
  const [isMapSaved, setIsMapSaved] = useState(false);
  const [isCameraFullScreen, setIsCameraFullScreen] = useState(false);
  const [isSpaceConfigured, setIsSpaceConfigured] = useState(false);
  const [showSpaceConfigModal, setShowSpaceConfigModal] = useState(false);
  const [showSterilizationModal, setShowSterilizationModal] = useState(false);
  const [spaceConfig, setSpaceConfig] = useState<SpaceConfigData | null>(null);

  const handleMapSaved = () => {
    setIsMapSaved(true);
  };

  const handleReset = () => {
    setIsMapSaved(false);
    setIsCameraFullScreen(false);
    setIsSpaceConfigured(false);
    setSpaceConfig(null);
  };

  const handleToggleFullScreen = () => {
    setIsCameraFullScreen(!isCameraFullScreen);
  };

  const handleMapClick = () => {
    if (!isMapSaved) return; // Only allow clicks after map is saved
    setShowSpaceConfigModal(true);
  };

  const handleSpaceConfigConfirm = (data: SpaceConfigData) => {
    setSpaceConfig(data);
    setIsSpaceConfigured(true);
    setShowSpaceConfigModal(false);
  };

  const handleStartSterilization = () => {
    setShowSterilizationModal(true);
  };

  const handleSterilizationConfirm = (data: SterilizationConfigData) => {
    console.log('Starting sterilization with config:', data);
    setShowSterilizationModal(false);
    // Here you would typically start the sterilization process
  };
  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="p-4 flex items-center gap-4 bg-gray-800">
      
        <span className="text-lg">Configuration / Scanning space</span>
      </header>

      {/* Main Content */}
      <main className="flex flex-1 overflow-hidden">
        {!isMapSaved ? (
          // Initial state: Three sections layout
          <>
            {/* Left Section */}
            <section className="flex-1 p-4 bg-gray-800 space-y-6 overflow-auto">
              <RobotStatusCard />
              <ParametersCard />
              <ActionButtons
                onMapSaved={handleMapSaved}
                onReset={handleReset}
                isMapSavedState={isMapSaved}
              />
            </section>

            {/* Right Section */}
            <section className="flex-1 p-4 flex flex-col">
              <div className="flex-1">
                <MapView />
              </div>
              <DirectionControls />
            </section>
          </>
        ) : (
          // After map saved: Two panel layout
          <>
            {/* Left Panel - Saved Map */}
            <section className="flex-1 p-4 flex flex-col">
              <div className="flex-1">
                <MapView
                  onMapClick={handleMapClick}
                  isClickable={true}
                />
              </div>
              {!isSpaceConfigured ? (
                <DirectionControls />
              ) : (
                <div className="mt-4">
                  <StartSterilizationButton onClick={handleStartSterilization} />
                </div>
              )}
            </section>

            {/* Right Panel - Camera View */}
            <section className="flex-1 p-4 flex flex-col">
              <CameraView
                isFullScreen={isCameraFullScreen}
                onToggleFullScreen={handleToggleFullScreen}
              />
              {/* Reset Button */}
              <div className="mt-4">
                <button
                  onClick={handleReset}
                  className="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded flex items-center justify-center gap-2 transition-colors"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                    <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8" />
                    <path d="M21 3v5h-5" />
                    <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16" />
                    <path d="M8 16H3v5" />
                  </svg>
                  Back to Configuration
                </button>
              </div>
            </section>
          </>
        )}
      </main>

      {/* Full Screen Camera Overlay */}
      {isCameraFullScreen && (
        <div className="fixed inset-0 z-50 bg-black">
          <CameraView
            isFullScreen={true}
            onToggleFullScreen={handleToggleFullScreen}
          />
        </div>
      )}

      {/* Space Configuration Modal */}
      <SpaceConfigModal
        isOpen={showSpaceConfigModal}
        onClose={() => setShowSpaceConfigModal(false)}
        onConfirm={handleSpaceConfigConfirm}
      />

      {/* Sterilization Configuration Modal */}
      <SterilizationConfigModal
        isOpen={showSterilizationModal}
        onClose={() => setShowSterilizationModal(false)}
        onConfirm={handleSterilizationConfirm}
      />
    </div>
  );
}