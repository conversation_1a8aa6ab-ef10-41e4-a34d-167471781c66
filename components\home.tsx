// app/Home.tsx
import { RobotStatusCard } from "@/components/ui/RobotStatusCard";
import { ParametersCard } from "@/components/ui/ParametersCard";
import { ActionButtons } from "@/components/ui/ActionButtons";
import { MapView } from "@/components/ui/MapView";
import { DirectionControls } from "@/components/ui/DirectionControls";

export function Home() {
  return (
    <div className="flex flex-col h-screen bg-gray-900 text-white">
      {/* Header */}
      <header className="p-4 flex items-center gap-4 bg-gray-800">
      
        <span className="text-lg">Configuration / Scanning space</span>
      </header>

      {/* Main Content */}
      <main className="flex flex-1 overflow-hidden">
        {/* Left Section */}
        <section className="flex-1 p-4 bg-gray-800 space-y-6 overflow-auto">
          <RobotStatusCard />
          <ParametersCard />
          <ActionButtons />
        </section>

        {/* Right Section */}
        <section className="flex-1 p-4 flex flex-col">
          <div className="flex-1">
            <MapView />
          </div>
          <DirectionControls />
        </section>
      </main>
    </div>
  );
}