// components/ui/NetworkConfigModal.tsx
import { useState } from 'react';
import { Wifi, Smartphone, Check } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface NetworkConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConnect: (connectionType: 'wifi' | 'mobile') => void;
}

export function NetworkConfigModal({ isOpen, onClose, onConnect }: NetworkConfigModalProps) {
  const [selectedConnection, setSelectedConnection] = useState<'wifi' | 'mobile' | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);

  const handleConnect = async () => {
    if (!selectedConnection) return;

    setIsConnecting(true);
    
    // Simulate connection process
    setTimeout(() => {
      onConnect(selectedConnection);
      setIsConnecting(false);
    }, 2000);
  };

  const handleClose = () => {
    if (!isConnecting) {
      setSelectedConnection(null);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
      <div className="relative z-10 w-full max-w-md">
        <div 
          className="rounded-xl shadow-2xl p-8 border border-[#0C6980]/20 modal-fade-in"
          style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
        >
          {/* Logo/Title Section */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-12 h-12 rounded-full overflow-hidden bg-white/10 flex items-center justify-center">
                <img 
                  src="/images/image.png" 
                  alt="SteriBOT Logo" 
                  className="w-10 h-10 object-cover rounded-full"
                  onError={(e) => {
                    e.currentTarget.style.display = 'none';
                    e.currentTarget.nextElementSibling!.style.display = 'flex';
                  }}
                />
                <div 
                  className="w-10 h-10 bg-white/20 rounded-full items-center justify-center text-white font-bold text-lg hidden"
                >
                  S
                </div>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-white">SteriBOT</h1>
                <p className="text-teal-100 text-sm">STERILISER</p>
              </div>
            </div>
            <h2 className="text-xl font-semibold text-white mb-2">Connect with</h2>
            <p className="text-teal-200 text-sm">
              Please connect to a WiFi network or enable your mobile data (4G/5G)
            </p>
          </div>

          {/* Connection Options */}
          <div className="space-y-4 mb-8">
            {/* WiFi Option */}
            <div
              onClick={() => !isConnecting && setSelectedConnection('wifi')}
              className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                selectedConnection === 'wifi'
                  ? 'border-white bg-white/10 shadow-lg'
                  : 'border-[#0C6980] bg-[#0A3F4C]/50 hover:border-white/50 hover:bg-white/5'
              } ${isConnecting ? 'cursor-not-allowed opacity-50' : ''}`}
            >
              <div className="flex items-center gap-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                  selectedConnection === 'wifi' ? 'bg-white/20' : 'bg-[#0C6980]/50'
                }`}>
                  <Wifi className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">Connect with WiFi</h3>
                  <p className="text-teal-200 text-sm">Use wireless network connection</p>
                </div>
                {selectedConnection === 'wifi' && (
                  <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                    <Check className="w-4 h-4 text-[#0A3F4C]" />
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Data Option */}
            <div
              onClick={() => !isConnecting && setSelectedConnection('mobile')}
              className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all duration-300 ${
                selectedConnection === 'mobile'
                  ? 'border-white bg-white/10 shadow-lg'
                  : 'border-[#0C6980] bg-[#0A3F4C]/50 hover:border-white/50 hover:bg-white/5'
              } ${isConnecting ? 'cursor-not-allowed opacity-50' : ''}`}
            >
              <div className="flex items-center gap-4">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center transition-colors ${
                  selectedConnection === 'mobile' ? 'bg-white/20' : 'bg-[#0C6980]/50'
                }`}>
                  <Smartphone className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-white font-medium">Connect with Mobile Data</h3>
                  <p className="text-teal-200 text-sm">Use cellular network (4G/5G)</p>
                </div>
                {selectedConnection === 'mobile' && (
                  <div className="w-6 h-6 rounded-full bg-white flex items-center justify-center">
                    <Check className="w-4 h-4 text-[#0A3F4C]" />
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isConnecting}
              className="flex-1 border-white text-white hover:bg-[#0C6980] hover:border-[#0C6980] disabled:opacity-50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConnect}
              disabled={!selectedConnection || isConnecting}
              className="flex-1 text-white disabled:opacity-50 disabled:cursor-not-allowed connect-button"
              style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
            >
              {isConnecting ? (
                <div className="flex items-center justify-center gap-2">
                  <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"></div>
                  Connecting...
                </div>
              ) : (
                'Connect'
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}
