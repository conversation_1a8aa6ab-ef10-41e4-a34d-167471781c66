// components/MapView.tsx
interface MapViewProps {
  onMapClick?: () => void;
  isClickable?: boolean;
}

export function MapView({ onMapClick, isClickable = false }: MapViewProps) {
  const handleClick = () => {
    if (isClickable && onMapClick) {
      onMapClick();
    }
  };

  return (
    <div
      className={`bg-gray-700 rounded-lg overflow-hidden flex-1 relative ${
        isClickable ? 'cursor-pointer hover:bg-gray-600 transition-colors' : ''
      }`}
      onClick={handleClick}
    >
      <img
        alt="Map"
        className="w-full h-full object-cover"
      />
      {isClickable && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-20 opacity-0 hover:opacity-100 transition-opacity rounded-lg">
          <div className="bg-gray-800 text-white px-4 py-2 rounded-lg text-sm">
            Click to configure space
          </div>
        </div>
      )}
    </div>
  )
}