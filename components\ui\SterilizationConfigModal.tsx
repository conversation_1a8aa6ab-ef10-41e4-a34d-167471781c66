// components/ui/SterilizationConfigModal.tsx
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface SterilizationConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: SterilizationConfigData) => void;
}

export interface SterilizationConfigData {
  floor: string;
  robot: string;
  date: string;
  time: string;
  bacteriaType: string;
}

const floorOptions = [
  "Floor 1",
  "Floor 2", 
  "Floor 3",
  "Basement",
  "Ground Floor"
];

const robotOptions = [
  "Robot-001",
  "Robot-002",
  "Robot-003", 
  "SteriBOT-Alpha",
  "SteriBOT-Beta"
];

const bacteriaTypes = [
  "E. coli",
  "Staphylococcus",
  "Streptococcus",
  "MRSA",
  "C. difficile",
  "All Types"
];

export function SterilizationConfigModal({ isOpen, onClose, onConfirm }: SterilizationConfigModalProps) {
  const [floor, setFloor] = useState('');
  const [robot, setRobot] = useState('');
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [bacteriaType, setBacteriaType] = useState('');

  const handleConfirm = () => {
    if (!floor || !robot || !date || !time || !bacteriaType) {
      return; // Basic validation
    }

    onConfirm({
      floor,
      robot,
      date,
      time,
      bacteriaType
    });

    // Reset form
    setFloor('');
    setRobot('');
    setDate('');
    setTime('');
    setBacteriaType('');
  };

  const handleCancel = () => {
    // Reset form
    setFloor('');
    setRobot('');
    setDate('');
    setTime('');
    setBacteriaType('');
    onClose();
  };

  // Get current date in YYYY-MM-DD format for min date
  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // Get current time in HH:MM format for min time (if today is selected)
  const getCurrentTime = () => {
    const now = new Date();
    return now.toTimeString().slice(0, 5);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg bg-gray-800 text-white border-gray-700">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-center">
            Sterilization Configuration
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* First Row: Floor and Robot */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="floor" className="text-sm font-medium">
                Floor
              </Label>
              <Select value={floor} onValueChange={setFloor}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select floor" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  {floorOptions.map((option) => (
                    <SelectItem 
                      key={option} 
                      value={option}
                      className="text-white hover:bg-gray-600"
                    >
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="robot" className="text-sm font-medium">
                Robot
              </Label>
              <Select value={robot} onValueChange={setRobot}>
                <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                  <SelectValue placeholder="Select robot" />
                </SelectTrigger>
                <SelectContent className="bg-gray-700 border-gray-600">
                  {robotOptions.map((option) => (
                    <SelectItem 
                      key={option} 
                      value={option}
                      className="text-white hover:bg-gray-600"
                    >
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Second Row: Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date" className="text-sm font-medium">
                Date
              </Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                min={getCurrentDate()}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="time" className="text-sm font-medium">
                Time
              </Label>
              <Input
                id="time"
                type="time"
                value={time}
                onChange={(e) => setTime(e.target.value)}
                min={date === getCurrentDate() ? getCurrentTime() : undefined}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
          </div>

          {/* Third Row: Bacteria Types */}
          <div className="space-y-2">
            <Label htmlFor="bacteriaType" className="text-sm font-medium">
              Bacteria Types
            </Label>
            <Select value={bacteriaType} onValueChange={setBacteriaType}>
              <SelectTrigger className="bg-gray-700 border-gray-600 text-white">
                <SelectValue placeholder="Select bacteria type" />
              </SelectTrigger>
              <SelectContent className="bg-gray-700 border-gray-600">
                {bacteriaTypes.map((type) => (
                  <SelectItem 
                    key={type} 
                    value={type}
                    className="text-white hover:bg-gray-600"
                  >
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1 border-gray-600 text-gray-300 hover:bg-gray-700"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!floor || !robot || !date || !time || !bacteriaType}
            className="flex-1 bg-green-600 hover:bg-green-700 text-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Start Sterilization
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
