// components/ui/SterilizationConfigModal.tsx
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";

interface SterilizationConfigModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (data: SterilizationConfigData) => void;
}

export interface SterilizationConfigData {
  floor: string;
  robot: string;
  date: string;
  time: string;
  bacteriaType: string;
}

const floorOptions = [
  "Floor 1",
  "Floor 2", 
  "Floor 3",
  "Basement",
  "Ground Floor"
];

const robotOptions = [
  "Robot-001",
  "Robot-002",
  "Robot-003", 
  "SteriBOT-Alpha",
  "SteriBOT-Beta"
];

const bacteriaTypes = [
  "E. coli",
  "Staphylococcus",
  "Streptococcus",
  "MRSA",
  "C. difficile",
  "All Types"
];

export function SterilizationConfigModal({ isOpen, onClose, onConfirm }: SterilizationConfigModalProps) {
  const [floor, setFloor] = useState('');
  const [robot, setRobot] = useState('');
  const [date, setDate] = useState('');
  const [time, setTime] = useState('');
  const [bacteriaType, setBacteriaType] = useState('');

  const handleConfirm = () => {
    if (!floor || !robot || !date || !time || !bacteriaType) {
      return; // Basic validation
    }

    onConfirm({
      floor,
      robot,
      date,
      time,
      bacteriaType
    });

    // Reset form
    setFloor('');
    setRobot('');
    setDate('');
    setTime('');
    setBacteriaType('');
  };

  const handleCancel = () => {
    // Reset form
    setFloor('');
    setRobot('');
    setDate('');
    setTime('');
    setBacteriaType('');
    onClose();
  };

  // Get current date in YYYY-MM-DD format for min date
  const getCurrentDate = () => {
    const today = new Date();
    return today.toISOString().split('T')[0];
  };

  // Get current time in HH:MM format for min time (if today is selected)
  const getCurrentTime = () => {
    const now = new Date();
    return now.toTimeString().slice(0, 5);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent
        className="sm:max-w-lg text-white border-[#0C6980]"
        style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
      >
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold text-center text-white">
            Sterilization Configuration
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* First Row: Floor and Robot */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="floor" className="text-sm font-medium text-white">
                Floor
              </Label>
              <Select value={floor} onValueChange={setFloor}>
                <SelectTrigger className="bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white">
                  <SelectValue placeholder="Select floor" />
                </SelectTrigger>
                <SelectContent
                  className="border-[#0C6980]"
                  style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
                >
                  {floorOptions.map((option) => (
                    <SelectItem
                      key={option}
                      value={option}
                      className="text-white hover:bg-[#0C6980] focus:bg-[#0C6980]"
                    >
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="robot" className="text-sm font-medium text-white">
                Robot
              </Label>
              <Select value={robot} onValueChange={setRobot}>
                <SelectTrigger className="bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white">
                  <SelectValue placeholder="Select robot" />
                </SelectTrigger>
                <SelectContent
                  className="border-[#0C6980]"
                  style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
                >
                  {robotOptions.map((option) => (
                    <SelectItem
                      key={option}
                      value={option}
                      className="text-white hover:bg-[#0C6980] focus:bg-[#0C6980]"
                    >
                      {option}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Second Row: Date and Time */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date" className="text-sm font-medium text-white">
                Date
              </Label>
              <Input
                id="date"
                type="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                min={getCurrentDate()}
                className="bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="time" className="text-sm font-medium text-white">
                Time
              </Label>
              <Input
                id="time"
                type="time"
                value={time}
                onChange={(e) => setTime(e.target.value)}
                min={date === getCurrentDate() ? getCurrentTime() : undefined}
                className="bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white"
              />
            </div>
          </div>

          {/* Third Row: Bacteria Types */}
          <div className="space-y-2">
            <Label htmlFor="bacteriaType" className="text-sm font-medium text-white">
              Bacteria Types
            </Label>
            <Select value={bacteriaType} onValueChange={setBacteriaType}>
              <SelectTrigger className="bg-[#0A3F4C] border-[#0C6980] text-white focus:border-white">
                <SelectValue placeholder="Select bacteria type" />
              </SelectTrigger>
              <SelectContent
                className="border-[#0C6980]"
                style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
              >
                {bacteriaTypes.map((type) => (
                  <SelectItem
                    key={type}
                    value={type}
                    className="text-white hover:bg-[#0C6980] focus:bg-[#0C6980]"
                  >
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-3 pt-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="flex-1 border-white text-black hover:bg-[#0C6980] hover:border-[#0C6980] hover:text-white"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={!floor || !robot || !date || !time || !bacteriaType}
            className="flex-1 text-white disabled:opacity-50 disabled:cursor-not-allowed"
            style={{ background: 'linear-gradient(90deg, #22c55e, #16a34a)' }}
          >
            Start Sterilization
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
