// components/RobotStatusCard.tsx
import { BatteryFull, Gauge } from "lucide-react"

interface RobotStatus {
  position: { x: number; y: number }
  battery: number
  speed: number
}

const initialRobotStatus: RobotStatus = {
  position: { x: 12.5, y: 8.3 },
  battery: 82,
  speed: 0.8,
}

export function RobotStatusCard() {
  const status = initialRobotStatus

  return (
    <div
      className="p-4 rounded-lg text-white"
      style={{ background: 'linear-gradient(90deg, #0A3F4C, #0C6980)' }}
    >
      <h2 className="flex items-center gap-2 text-white font-bold mb-4">
        <Gauge className="w-5 h-5" />
        Robot Status
      </h2>
      <div className="space-y-3 text-white">
        <div className="flex justify-between">
          <span className="text-gray-300">Position</span>
          <span>X: {status.position.x}, Y: {status.position.y}</span>
        </div>
        <div className="flex justify-between items-center">
          <span className="text-gray-300">Battery</span>
          <div className="flex items-center gap-2">
            <div className="w-24 h-2 bg-[#0A3F4C] rounded-full overflow-hidden border border-[#0C6980]">
              <div
                className="h-full rounded-full"
                style={{
                  width: `${status.battery}%`,
                  background: 'linear-gradient(90deg, #14b8a6, #0C6980)'
                }}
              ></div>
            </div>
            <span>{status.battery}%</span>
          </div>
        </div>
        <div className="flex justify-between">
          <span className="text-gray-300">Speed</span>
          <span>{status.speed} m/s</span>
        </div>
      </div>
    </div>
  )
}